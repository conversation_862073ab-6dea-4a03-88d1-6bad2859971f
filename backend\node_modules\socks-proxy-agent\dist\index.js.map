{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,iCAAmE;AACnE,2CAAiE;AAEjE,kDAA+B;AAE/B,8CAAqB;AAErB,8CAAqB;AAkBrB,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,mBAAmB,CAAC,CAAA;AAE9C,SAAS,eAAe,CAAE,IAA4B;;IACpD,IAAI,IAAI,GAAG,CAAC,CAAA;IACZ,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,IAAI,IAAI,GAAuB,CAAC,CAAA;IAEhC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAA;IAEvC,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,CAAA;KACrC;IAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;KACjB;SAAM,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QACxC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;KAC/B;IAED,0EAA0E;IAC1E,iEAAiE;IACjE,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,IAAI,GAAG,IAAI,CAAA;KACZ;IAED,sEAAsE;IACtE,iBAAiB;IACjB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;QACzB,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;YACtC,KAAK,QAAQ;gBACX,MAAM,GAAG,IAAI,CAAA;YACf,eAAe;YACf,KAAK,SAAS;gBACZ,IAAI,GAAG,CAAC,CAAA;gBACR,MAAK;YACP,KAAK,QAAQ;gBACX,MAAM,GAAG,IAAI,CAAA;YACf,eAAe;YACf,KAAK,OAAO,CAAC,CAAC,sCAAsC;YACpD,KAAK,SAAS;gBACZ,IAAI,GAAG,CAAC,CAAA;gBACR,MAAK;YACP;gBACE,MAAM,IAAI,SAAS,CAAC,8CAA8C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;SAC7F;KACF;IAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;QACpC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;YACtC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;SACjB;aAAM;YACL,MAAM,IAAI,SAAS,CAAC,+BAA+B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACxE;KACF;IAED,MAAM,KAAK,GAAe;QACxB,IAAI;QACJ,IAAI;QACJ,IAAI;KACL,CAAA;IAED,IAAI,MAAM,GAAG,MAAA,IAAI,CAAC,MAAM,mCAAI,IAAI,CAAC,QAAQ,CAAA;IACzC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC5B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACjC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QAChB,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;KACnB;IACD,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;YACrC,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB,CAAC,CAAA;KACH;IACD,IAAI,QAAQ,IAAI,IAAI,EAAE;QACpB,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE;YACvC,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,KAAK;SAClB,CAAC,CAAA;KACH;IAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;AAC1B,CAAC;AAED,MAAM,qBAAqB,GAAG,CAAC,KAAsC,EAA0B,EAAE;IAC/F,IAAI,YAAoC,CAAA;IACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,YAAY,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAA;KAC9B;SAAM;QACL,YAAY,GAAG,KAAK,CAAA;KACrB;IACD,IAAI,YAAY,IAAI,IAAI,EAAE;QACxB,MAAM,IAAI,SAAS,CAAC,+DAA+D,CAAC,CAAA;KACrF;IAED,OAAO,YAAY,CAAA;AACrB,CAAC,CAAA;AAID,MAAa,eAAgB,SAAQ,kBAAK;IAMxC,YAAa,KAAsC,EAAE,OAAqC;;QACxF,MAAM,YAAY,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,KAAK,CAAC,YAAY,CAAC,CAAA;QAEnB,MAAM,WAAW,GAAG,eAAe,CAAC,YAAY,CAAC,CAAA;QAEjD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,MAAM,CAAA;QACtC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAA;QAC9B,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;QAC5E,IAAI,CAAC,OAAO,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,mCAAI,IAAI,CAAA;IACzC,CAAC;IAED;;;;;OAKG;IACG,QAAQ,CAAE,GAAkB,EAAE,IAAoB;;;YACtD,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAA;YAE7C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAA;YAEjD,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;aACtC;YAED,IAAI,YAAY,EAAE;gBAChB,mEAAmE;gBACnE,IAAI,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACnD,0DAA0D;oBAC1D,MAAM,QAAQ,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,aAAG,CAAC,MAAM,CAAA;oBAC7C,QAAQ,CAAC,IAAK,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBAC/B,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,GAAG,CAAC,CAAA;yBACZ;6BAAM;4BACL,OAAO,CAAC,GAAG,CAAC,CAAA;yBACb;oBACH,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;aACH;YAED,MAAM,SAAS,GAAuB;gBACpC,KAAK;gBACL,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;gBAC3B,OAAO,EAAE,SAAS;gBAClB,OAAO,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,SAAS;aAC9B,CAAA;YAED,MAAM,OAAO,GAAG,CAAC,SAAyB,EAAE,EAAE;gBAC5C,GAAG,CAAC,OAAO,EAAE,CAAA;gBACb,MAAM,CAAC,OAAO,EAAE,CAAA;gBAChB,IAAI,SAAS;oBAAE,SAAS,CAAC,OAAO,EAAE,CAAA;YACpC,CAAC,CAAA;YAED,KAAK,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAA;YACvD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mBAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;YAChE,KAAK,CAAC,6CAA6C,CAAC,CAAA;YAEpD,IAAI,OAAO,KAAK,IAAI,EAAE;gBACpB,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;gBAC1B,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;aACtC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,sDAAsD;gBACtD,8CAA8C;gBAC9C,KAAK,CAAC,oCAAoC,CAAC,CAAA;gBAC3C,MAAM,UAAU,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,IAAI,CAAC,IAAI,CAAA;gBAE/C,MAAM,SAAS,GAAG,aAAG,CAAC,OAAO,+CACxB,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,KACjD,MAAM;oBACN,UAAU,KACP,IAAI,CAAC,oBAAoB,EAC5B,CAAA;gBAEF,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBAChC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;oBACxC,OAAO,CAAC,SAAS,CAAC,CAAA;gBACpB,CAAC,CAAC,CAAA;gBAEF,OAAO,SAAS,CAAA;aACjB;YAED,OAAO,MAAM,CAAA;;KACd;CACF;AA7FD,0CA6FC;AAED,SAAS,IAAI,CACX,GAAM,EACN,GAAG,IAAO;IAIV,MAAM,GAAG,GAAG,EAAgD,CAAA;IAC5D,IAAI,GAAqB,CAAA;IACzB,KAAK,GAAG,IAAI,GAAG,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;SACpB;KACF;IACD,OAAO,GAAG,CAAA;AACZ,CAAC"}