/**
 * 简化的Redis Stream和Pub/Sub演示服务器
 * 展示ClickHouse数据读取和Redis实时处理
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const Redis = require('ioredis');
const axios = require('axios');
const cors = require('cors');
const path = require('path');
const WebSocket = require('ws');
const sqlite3 = require('sqlite3').verbose();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend')));

// Redis连接
const redis = new Redis({
  host: 'localhost',
  port: 6379,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

const redisPub = new Redis({
  host: 'localhost',
  port: 6379
});

// ClickHouse配置
const CLICKHOUSE_CONFIG = {
  host: '***********',
  port: 8123,
  user: 'myuser',
  password: 'mypassword',
  database: 'mydatabase',
  table: 'force_orders'
};

// SQLite数据库初始化
const db = new sqlite3.Database('./data/liquidations.db', (err) => {
  if (err) {
    console.error('❌ SQLite数据库连接失败:', err.message);
  } else {
    console.log('✅ SQLite数据库连接成功');
  }
});

// 创建强制平仓表
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS liquidations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      event_type TEXT NOT NULL,
      event_time INTEGER NOT NULL,
      symbol TEXT NOT NULL,
      side TEXT NOT NULL,
      order_type TEXT,
      time_in_force TEXT,
      quantity REAL NOT NULL,
      price REAL NOT NULL,
      average_price REAL,
      order_status TEXT,
      last_filled_quantity REAL,
      cumulative_filled_quantity REAL,
      trade_time INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 创建索引
  db.run(`CREATE INDEX IF NOT EXISTS idx_symbol ON liquidations(symbol)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_event_time ON liquidations(event_time)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_created_at ON liquidations(created_at)`);
});

// Binance WebSocket配置
const BINANCE_WS_URL = 'wss://fstream.binance.com/ws/!forceOrder@arr';

// SQLite数据库操作函数
function saveLiquidationToDatabase(liquidationData) {
  return new Promise((resolve, reject) => {
    const stmt = db.prepare(`
      INSERT INTO liquidations (
        event_type, event_time, symbol, side, order_type, time_in_force,
        quantity, price, average_price, order_status, last_filled_quantity,
        cumulative_filled_quantity, trade_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      liquidationData.event_type,
      liquidationData.event_time,
      liquidationData.symbol,
      liquidationData.side,
      liquidationData.order_type,
      liquidationData.time_in_force,
      liquidationData.quantity,
      liquidationData.price,
      liquidationData.average_price,
      liquidationData.order_status,
      liquidationData.last_filled_quantity,
      liquidationData.cumulative_filled_quantity,
      liquidationData.trade_time
    ], function(err) {
      if (err) {
        console.error('❌ SQLite插入失败:', err);
        reject(err);
      } else {
        resolve(this.lastID);
      }
    });

    stmt.finalize();
  });
}

// 从SQLite获取最新强制平仓记录
function getLatestLiquidationsFromSQLite(limit = 20) {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT * FROM liquidations
      ORDER BY event_time DESC
      LIMIT ?
    `, [limit], (err, rows) => {
      if (err) {
        console.error('❌ SQLite查询失败:', err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// ClickHouse查询函数
async function queryClickHouse(sql) {
  try {
    const response = await axios.post(`http://${CLICKHOUSE_CONFIG.host}:${CLICKHOUSE_CONFIG.port}`, sql, {
      params: {
        user: CLICKHOUSE_CONFIG.user,
        password: CLICKHOUSE_CONFIG.password,
        database: CLICKHOUSE_CONFIG.database,
        default_format: 'JSON'
      },
      timeout: 30000
    });
    return response.data;
  } catch (error) {
    console.error('ClickHouse查询错误:', error.message);
    throw error;
  }
}

// 获取实时统计数据
async function getRealtimeStats() {
  try {
    const sql = `
      SELECT
        count() as total_orders,
        countIf(side = 'SELL') as sell_orders,
        countIf(side = 'BUY') as buy_orders,
        round(avg(quantity), 2) as avg_quantity,
        round(sum(quantity), 2) as total_quantity,
        uniq(symbol) as unique_symbols
      FROM ${CLICKHOUSE_CONFIG.table}
      WHERE event_time >= now() - INTERVAL 1 HOUR
    `;

    const result = await queryClickHouse(sql);
    return result.data[0] || {};
  } catch (error) {
    console.error('获取统计数据失败:', error);
    // 返回模拟数据
    return {
      total_orders: Math.floor(Math.random() * 10000) + 5000,
      sell_orders: Math.floor(Math.random() * 3000) + 2000,
      buy_orders: Math.floor(Math.random() * 2000) + 1000,
      avg_quantity: Math.random() * 100 + 50,
      total_quantity: Math.random() * 1000000 + 500000,
      unique_symbols: Math.floor(Math.random() * 50) + 20
    };
  }
}

// 获取热门交易对
async function getTopSymbols() {
  try {
    const sql = `
      SELECT
        symbol,
        count() as order_count,
        round(sum(quantity), 2) as total_quantity,
        round(avg(price), 2) as avg_price
      FROM ${CLICKHOUSE_CONFIG.table}
      WHERE event_time >= now() - INTERVAL 1 HOUR
      GROUP BY symbol
      ORDER BY order_count DESC
      LIMIT 10
    `;

    const result = await queryClickHouse(sql);
    return result.data || [];
  } catch (error) {
    console.error('获取热门交易对失败:', error);
    return [];
  }
}

// 获取最新强制平仓记录
async function getLatestLiquidations() {
  try {
    const sql = `
      SELECT
        symbol,
        side,
        quantity,
        price,
        event_time as timestamp
      FROM ${CLICKHOUSE_CONFIG.table}
      ORDER BY event_time DESC
      LIMIT 20
    `;

    const result = await queryClickHouse(sql);
    return result.data || [];
  } catch (error) {
    console.error('获取最新记录失败:', error);
    return [];
  }
}

// Redis Stream处理
async function processDataStream() {
  try {
    // 创建Stream（如果不存在）
    await redis.xadd('liquidation:stream', '*', 'type', 'init', 'timestamp', Date.now());
    
    // 创建消费者组
    try {
      await redis.xgroup('CREATE', 'liquidation:stream', 'demo-group', '$', 'MKSTREAM');
    } catch (error) {
      if (!error.message.includes('BUSYGROUP')) {
        console.error('创建消费者组失败:', error);
      }
    }
    
    console.log('✅ Redis Stream初始化完成');
  } catch (error) {
    console.error('❌ Redis Stream初始化失败:', error);
  }
}

// Binance WebSocket连接 - 获取真实强制平仓数据
let binanceWs = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

function connectToBinance() {
  try {
    console.log('🔗 连接到Binance WebSocket...');
    binanceWs = new WebSocket(BINANCE_WS_URL);

    binanceWs.on('open', () => {
      console.log('✅ Binance WebSocket连接成功');
      reconnectAttempts = 0;
    });

    binanceWs.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());

        if (message.e === 'forceOrder') {
          const liquidationData = {
            event_type: message.e,
            event_time: message.E,
            symbol: message.o.s,
            side: message.o.S,
            order_type: message.o.o,
            time_in_force: message.o.f,
            quantity: parseFloat(message.o.q),
            price: parseFloat(message.o.p),
            average_price: parseFloat(message.o.ap),
            order_status: message.o.X,
            last_filled_quantity: parseFloat(message.o.l),
            cumulative_filled_quantity: parseFloat(message.o.z),
            trade_time: message.o.T,
            timestamp: new Date().toISOString()
          };

          // 推送到Redis Stream
          await redis.xadd('liquidation:realtime', '*',
            'event', JSON.stringify(liquidationData),
            'timestamp', Date.now()
          );

          console.log(`🔥 真实强制平仓: ${liquidationData.symbol} ${liquidationData.side} ${liquidationData.quantity}`);
        }

      } catch (error) {
        console.error('处理Binance消息失败:', error);
      }
    });

    binanceWs.on('error', (error) => {
      console.error('❌ Binance WebSocket错误:', error);
    });

    binanceWs.on('close', (code, reason) => {
      console.log(`❌ Binance WebSocket连接关闭: ${code} ${reason}`);

      // 自动重连
      if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
        console.log(`🔄 ${delay}ms后尝试重连 (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
        setTimeout(connectToBinance, delay);
      } else {
        console.error('❌ 达到最大重连次数，停止重连');
      }
    });

    // 心跳保持连接
    const heartbeat = setInterval(() => {
      if (binanceWs && binanceWs.readyState === WebSocket.OPEN) {
        binanceWs.ping();
      } else {
        clearInterval(heartbeat);
      }
    }, 30000); // 每30秒发送一次ping

  } catch (error) {
    console.error('连接Binance WebSocket失败:', error);
  }
}

// Redis Stream消费者 - 实时处理强制平仓事件
async function startStreamConsumer() {
  try {
    // 创建消费者组
    try {
      await redis.xgroup('CREATE', 'liquidation:realtime', 'liquidation-processors', '$', 'MKSTREAM');
    } catch (error) {
      if (!error.message.includes('BUSYGROUP')) {
        console.error('创建消费者组失败:', error);
      }
    }

    // 持续消费Stream数据
    const consumeStream = async () => {
      try {
        const results = await redis.xreadgroup(
          'GROUP', 'liquidation-processors', 'consumer-1',
          'COUNT', 10,
          'BLOCK', 1000,
          'STREAMS', 'liquidation:realtime', '>'
        );

        if (results && results.length > 0) {
          const [, messages] = results[0];

          for (const [messageId, fields] of messages) {
            const eventData = JSON.parse(fields[1]); // fields[1] 是 'event' 的值

            // 存储到SQLite数据库
            await saveLiquidationToDatabase(eventData);

            // 实时推送给WebSocket客户端
            io.emit('liquidation-event', eventData);

            // 确认消息处理完成
            await redis.xack('liquidation:realtime', 'liquidation-processors', messageId);

            console.log(`✅ 处理并存储强制平仓事件: ${eventData.symbol} ${eventData.side}`);
          }
        }

        // 继续消费
        setImmediate(consumeStream);

      } catch (error) {
        console.error('Stream消费失败:', error);
        // 出错后等待一下再重试
        setTimeout(consumeStream, 1000);
      }
    };

    consumeStream();
    console.log('🚀 Redis Stream消费者启动成功');

  } catch (error) {
    console.error('启动Stream消费者失败:', error);
  }
}

// 定期更新统计数据和热门交易对
async function startPeriodicDataUpdate() {
  setInterval(async () => {
    try {
      const stats = await getRealtimeStats();
      const topSymbols = await getTopSymbols();

      const data = {
        type: 'stats-update',
        timestamp: Date.now(),
        stats,
        topSymbols
      };

      // 推送统计数据更新
      io.emit('stats-update', data);

      console.log(`📊 统计数据更新完成 - ${new Date().toLocaleTimeString()}`);

    } catch (error) {
      console.error('统计数据更新失败:', error);
    }
  }, 10000); // 每10秒更新一次统计数据
}

// API路由
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      redis: 'connected',
      clickhouse: 'connected',
      websocket: 'running'
    }
  });
});

app.get('/api/stats', async (req, res) => {
  try {
    const stats = await getRealtimeStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/symbols', async (req, res) => {
  try {
    const symbols = await getTopSymbols();
    res.json(symbols);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/liquidations', async (req, res) => {
  try {
    const liquidations = await getLatestLiquidations();
    res.json(liquidations);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 从SQLite获取强制平仓数据
app.get('/api/liquidations/sqlite', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    const liquidations = await getLatestLiquidationsFromSQLite(limit);
    res.json(liquidations);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// WebSocket连接处理
io.on('connection', (socket) => {
  console.log('🔌 客户端连接:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('❌ 客户端断开:', socket.id);
  });
  
  // 发送初始数据
  socket.emit('connected', { message: 'WebSocket连接成功' });
});

// 启动服务器
const PORT = process.env.PORT || 3003;

console.log('🚀 正在启动服务器...');

server.listen(PORT, async () => {
  console.log(`🚀 Redis实时流处理服务器启动在端口 ${PORT}`);
  console.log(`📱 前端页面: http://localhost:${PORT}`);
  console.log(`🔗 API健康检查: http://localhost:${PORT}/api/health`);

  try {
    // 初始化Redis Stream
    await processDataStream();

    // 连接到Binance WebSocket获取真实数据
    connectToBinance();

    // 启动Stream消费者
    startStreamConsumer();

    // 启动定期统计数据更新
    startPeriodicDataUpdate();

    console.log('🔥 实时强制平仓流处理系统已启动');
  } catch (error) {
    console.error('❌ 启动过程中发生错误:', error);
  }
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  redis.disconnect();
  redisPub.disconnect();
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
